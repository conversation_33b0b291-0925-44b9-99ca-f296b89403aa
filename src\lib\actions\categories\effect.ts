// 特效类动作定义

import { CategoryIds, ActionIds } from "../../constants";
import type { Action } from "../../types/action";
import { applyActionMapping } from "../action-mappings";

// 特效类动作定义
const rawEffectActions = [
  {
    id: ActionIds.FADE_IN,
    nameKey: "actions.fade_in.name",
    descriptionKey: "actions.fade_in.description",
    categoryId: CategoryIds.EFFECT,
    inputTypes: ["video"],
    outputTypes: ["video"],
    preview: true,
    order: 10,
    params: [
      {
        key: "duration",
        type: "duration",
        nameKey: "actions.fade_in.params.duration",
        required: true,
        defaultValue: 1.0,
        min: 0.1,
        max: 10.0,
        step: 0.1,
      },
    ],
    validate: (params) => {
      const errors = [];
      if (params.duration < 0.1 || params.duration > 10.0) {
        errors.push("actions.fade_in.errors.invalid_duration");
      }
      return {
        isValid: errors.length === 0,
        errors,
      };
    },
  },
  {
    id: ActionIds.FADE_OUT,
    nameKey: "actions.fade_out.name",
    descriptionKey: "actions.fade_out.description",
    categoryId: CategoryIds.EFFECT,
    inputTypes: ["video"],
    outputTypes: ["video"],
    preview: true,
    order: 20,
    params: [
      {
        key: "duration",
        type: "duration",
        nameKey: "actions.fade_out.params.duration",
        required: true,
        defaultValue: 1.0,
        min: 0.1,
        max: 10.0,
        step: 0.1,
      },
    ],
    validate: (params) => {
      const errors = [];
      if (params.duration < 0.1 || params.duration > 10.0) {
        errors.push("actions.fade_out.errors.invalid_duration");
      }
      return {
        isValid: errors.length === 0,
        errors,
      };
    },
  },
  {
    id: ActionIds.BLUR_EFFECT,
    nameKey: "actions.blur_effect.name",
    descriptionKey: "actions.blur_effect.description",
    categoryId: CategoryIds.EFFECT,
    inputTypes: ["video"],
    outputTypes: ["video"],
    preview: true,
    order: 30,
    params: [
      {
        key: "intensity",
        type: "range",
        nameKey: "actions.blur_effect.params.intensity",
        required: true,
        defaultValue: 5,
        min: 1,
        max: 20,
        step: 1,
      },
    ],
    validate: (params) => {
      const errors = [];
      if (params.intensity < 1 || params.intensity > 20) {
        errors.push("actions.blur_effect.errors.invalid_intensity");
      }
      return {
        isValid: errors.length === 0,
        errors,
      };
    },
  },
  {
    id: ActionIds.SHARPEN_EFFECT,
    nameKey: "actions.sharpen_effect.name",
    descriptionKey: "actions.sharpen_effect.description",
    categoryId: CategoryIds.EFFECT,
    inputTypes: ["video"],
    outputTypes: ["video"],
    preview: true,
    order: 40,
    params: [
      {
        key: "intensity",
        type: "range",
        nameKey: "actions.sharpen_effect.params.intensity",
        required: true,
        defaultValue: 1.0,
        min: 0.1,
        max: 3.0,
        step: 0.1,
      },
    ],
    validate: (params) => {
      const errors = [];
      if (params.intensity < 0.1 || params.intensity > 3.0) {
        errors.push("actions.sharpen_effect.errors.invalid_intensity");
      }
      return {
        isValid: errors.length === 0,
        errors,
      };
    },
  },
  {
    id: ActionIds.REVERSE_VIDEO,
    nameKey: "actions.reverse_video.name",
    descriptionKey: "actions.reverse_video.description",
    categoryId: CategoryIds.EFFECT,
    inputTypes: ["video"],
    outputTypes: ["video"],
    preview: true,
    order: 50,
    params: [
      {
        key: "keepAudio",
        type: "boolean",
        nameKey: "actions.reverse_video.params.keepAudio",
        required: false,
        defaultValue: false,
      },
    ],
  },
  {
    id: ActionIds.ADJUST_SPEED,
    nameKey: "actions.adjust_speed.name",
    descriptionKey: "actions.adjust_speed.description",
    categoryId: CategoryIds.EFFECT,
    inputTypes: ["video"],
    outputTypes: ["video"],
    preview: true,
    order: 60,
    params: [
      {
        key: "speed",
        type: "range",
        nameKey: "actions.adjust_speed.params.speed",
        required: true,
        defaultValue: 1.0,
        min: 0.1,
        max: 10.0,
        step: 0.1,
      },
    ],
    validate: (params) => {
      const errors = [];
      if (params.speed < 0.1 || params.speed > 10.0) {
        errors.push("actions.adjust_speed.errors.invalid_speed");
      }
      return {
        isValid: errors.length === 0,
        errors,
      };
    },
  },
];

// 应用映射并导出
export const effectActions: Action[] = rawEffectActions.map(applyActionMapping);
