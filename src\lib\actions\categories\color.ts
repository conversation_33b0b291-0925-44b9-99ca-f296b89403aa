// 颜色调整类动作定义

import { CategoryIds, ActionIds } from "../../constants";
import type { Action, ActionParams, ActionContext } from "../../types/action";
import { applyActionMapping } from "../action-mappings";

// 颜色调整类动作定义
const rawColorActions = [
  {
    id: ActionIds.ADJUST_BRIGHTNESS,
    nameKey: "actions.adjust_brightness.name",
    descriptionKey: "actions.adjust_brightness.description",
    categoryId: CategoryIds.COLOR,
    inputTypes: ["video"],
    outputTypes: ["video"],
    preview: true,
    order: 10,
    params: [
      {
        key: "brightness",
        type: "range",
        nameKey: "actions.adjust_brightness.params.brightness",
        required: true,
        defaultValue: 0,
        min: -1,
        max: 1,
        step: 0.1,
      },
    ],
    validate: (params) => {
      const errors = [];
      const brightness = parseFloat(params.brightness);
      if (isNaN(brightness)) {
        errors.push("actions.adjust_brightness.errors.invalid_number");
      } else if (brightness < -1 || brightness > 1) {
        errors.push("actions.adjust_brightness.errors.invalid_range");
      }
      return {
        isValid: errors.length === 0,
        errors,
      };
    },
    invokeCommand: "adjust_brightness",
    buildParams: (params: ActionParams, context: ActionContext) => ({
      inputPath: context.inputPath,
      outputPath: context.outputPath,
      brightness: parseFloat(params.brightness) || 0,
    }),
  },
  {
    id: ActionIds.ADJUST_CONTRAST,
    nameKey: "actions.adjust_contrast.name",
    descriptionKey: "actions.adjust_contrast.description",
    categoryId: CategoryIds.COLOR,
    inputTypes: ["video"],
    outputTypes: ["video"],
    preview: true,
    order: 20,
    params: [
      {
        key: "contrast",
        type: "range",
        nameKey: "actions.adjust_contrast.params.contrast",
        required: true,
        defaultValue: 1.0,
        min: 0,
        max: 3,
        step: 0.1,
      },
    ],
    validate: (params) => {
      const errors = [];
      if (params.contrast < 0 || params.contrast > 3) {
        errors.push("actions.adjust_contrast.errors.invalid_range");
      }
      return {
        isValid: errors.length === 0,
        errors,
      };
    },
  },
  {
    id: ActionIds.ADJUST_SATURATION,
    nameKey: "actions.adjust_saturation.name",
    descriptionKey: "actions.adjust_saturation.description",
    categoryId: CategoryIds.COLOR,
    inputTypes: ["video"],
    outputTypes: ["video"],
    preview: true,
    order: 30,
    params: [
      {
        key: "saturation",
        type: "range",
        nameKey: "actions.adjust_saturation.params.saturation",
        required: true,
        defaultValue: 1.0,
        min: 0,
        max: 3,
        step: 0.1,
      },
    ],
    validate: (params) => {
      const errors = [];
      if (params.saturation < 0 || params.saturation > 3) {
        errors.push("actions.adjust_saturation.errors.invalid_range");
      }
      return {
        isValid: errors.length === 0,
        errors,
      };
    },
  },
  {
    id: ActionIds.ADJUST_HUE,
    nameKey: "actions.adjust_hue.name",
    descriptionKey: "actions.adjust_hue.description",
    categoryId: CategoryIds.COLOR,
    inputTypes: ["video"],
    outputTypes: ["video"],
    preview: true,
    order: 40,
    params: [
      {
        key: "hue",
        type: "range",
        nameKey: "actions.adjust_hue.params.hue",
        required: true,
        defaultValue: 0,
        min: -180,
        max: 180,
        step: 1,
      },
    ],
    validate: (params) => {
      const errors = [];
      if (params.hue < -180 || params.hue > 180) {
        errors.push("actions.adjust_hue.errors.invalid_range");
      }
      return {
        isValid: errors.length === 0,
        errors,
      };
    },
  },
  {
    id: ActionIds.ADJUST_GAMMA,
    nameKey: "actions.adjust_gamma.name",
    descriptionKey: "actions.adjust_gamma.description",
    categoryId: CategoryIds.COLOR,
    inputTypes: ["video"],
    outputTypes: ["video"],
    preview: true,
    order: 50,
    params: [
      {
        key: "gamma",
        type: "range",
        nameKey: "actions.adjust_gamma.params.gamma",
        required: true,
        defaultValue: 1.0,
        min: 0.1,
        max: 3.0,
        step: 0.1,
      },
    ],
    validate: (params) => {
      const errors = [];
      if (params.gamma < 0.1 || params.gamma > 3.0) {
        errors.push("actions.adjust_gamma.errors.invalid_range");
      }
      return {
        isValid: errors.length === 0,
        errors,
      };
    },
  },
  {
    id: ActionIds.WHITE_BALANCE,
    nameKey: "actions.white_balance.name",
    descriptionKey: "actions.white_balance.description",
    categoryId: CategoryIds.COLOR,
    inputTypes: ["video"],
    outputTypes: ["video"],
    preview: true,
    order: 60,
    params: [
      {
        key: "temperature",
        type: "range",
        nameKey: "actions.white_balance.params.temperature",
        required: true,
        defaultValue: 6500,
        min: 2000,
        max: 11000,
        step: 100,
      },
    ],
    validate: (params) => {
      const errors = [];
      if (params.temperature < 2000 || params.temperature > 11000) {
        errors.push("actions.white_balance.errors.invalid_range");
      }
      return {
        isValid: errors.length === 0,
        errors,
      };
    },
  },
];

// 应用映射并导出
export const colorActions: Action[] = rawColorActions.map(applyActionMapping);
