// 裁剪类动作定义

import { CategoryIds, ActionIds } from "../../constants";
import type {
  Action,
  Category,
  ActionParams,
  ActionContext,
} from "../../types/action";

// 裁剪类别定义
export const trimCategory: Category = {
  id: CategoryIds.TRIM,
  nameKey: "categories.trim.name",
  descriptionKey: "categories.trim.description",
  order: 10,
};

// 裁剪类动作定义
export const trimActions: Action[] = [
  {
    id: ActionIds.TRIM_START,
    nameKey: "actions.trim_start.name",
    descriptionKey: "actions.trim_start.description",
    categoryId: CategoryIds.TRIM,
    inputTypes: ["video", "audio"],
    outputTypes: ["video", "audio"],
    preview: true,
    order: 10,
    params: [
      {
        key: "startDuration",
        type: "duration",
        nameKey: "actions.trim_start.params.startDuration",
        required: true,
        defaultValue: 0,
        min: 0,
      },
    ],
    validate: (params) => {
      const errors = [];
      const startDuration = parseFloat(params.startDuration);
      if (isNaN(startDuration)) {
        errors.push("actions.trim_start.errors.invalid_number");
      } else if (startDuration < 0) {
        errors.push("actions.trim_start.errors.negative_duration");
      }
      return {
        isValid: errors.length === 0,
        errors,
      };
    },
    invokeCommand: "trim_video_start",
    buildParams: (params: ActionParams, context: ActionContext) => {
      const startDuration = parseFloat(params.startDuration);
      return {
        inputPath: context.inputPath,
        outputPath: context.outputPath,
        startDuration: isNaN(startDuration) ? 10.0 : startDuration,
      };
    },
  },
  {
    id: ActionIds.TRIM_END,
    nameKey: "actions.trim_end.name",
    descriptionKey: "actions.trim_end.description",
    categoryId: CategoryIds.TRIM,
    inputTypes: ["video", "audio"],
    outputTypes: ["video", "audio"],
    preview: true,
    order: 20,
    params: [
      {
        key: "endTime",
        type: "duration",
        nameKey: "actions.trim_end.params.endTime",
        required: true,
        defaultValue: 0,
        min: 0,
      },
    ],
    validate: (params) => {
      const errors = [];
      const endTime = parseFloat(params.endTime);
      if (isNaN(endTime)) {
        errors.push("actions.trim_end.errors.invalid_number");
      } else if (endTime < 0) {
        errors.push("actions.trim_end.errors.negative_duration");
      }
      return {
        isValid: errors.length === 0,
        errors,
      };
    },
    invokeCommand: "trim_video_end",
    buildParams: (params: ActionParams, context: ActionContext) => {
      const endTime = parseFloat(params.endTime);
      return {
        inputPath: context.inputPath,
        outputPath: context.outputPath,
        endTime: isNaN(endTime) ? 10.0 : endTime,
      };
    },
  },
  {
    id: ActionIds.TRIM_SEGMENT,
    nameKey: "actions.trim_segment.name",
    descriptionKey: "actions.trim_segment.description",
    categoryId: CategoryIds.TRIM,
    inputTypes: ["video", "audio"],
    outputTypes: ["video", "audio"],
    preview: true,
    order: 30,
    params: [
      {
        key: "segments",
        type: "string", // 特殊类型，实际上是数组
        nameKey: "actions.trim_segment.params.segments",
        required: true,
        defaultValue: [],
      },
    ],
    validate: (params) => {
      const errors = [];
      if (
        !params.segments ||
        !Array.isArray(params.segments) ||
        params.segments.length === 0
      ) {
        errors.push("actions.trim_segment.errors.no_segments");
      }
      return {
        isValid: errors.length === 0,
        errors,
      };
    },
    invokeCommand: "trim_and_concat_segments",
    buildParams: (params: ActionParams, context: ActionContext) => {
      // 解析时间字符串 (HH:MM:SS)
      function timeStringToSeconds(timeStr: string): number {
        const parts = timeStr.split(":");
        const hours = parseInt(parts[0]) || 0;
        const minutes = parseInt(parts[1]) || 0;
        const seconds = parseInt(parts[2]) || 0;
        return hours * 3600 + minutes * 60 + seconds;
      }

      // 转换片段格式为后端需要的格式
      const segmentTuples = params.segments.map((segment: any) => [
        timeStringToSeconds(segment.startTimeStr),
        timeStringToSeconds(segment.endTimeStr),
      ]);

      return {
        inputPath: context.inputPath,
        outputPath: context.outputPath,
        segments: segmentTuples,
      };
    },
  },
  {
    id: ActionIds.EXCLUDE_SEGMENT,
    nameKey: "actions.exclude_segment.name",
    descriptionKey: "actions.exclude_segment.description",
    categoryId: CategoryIds.TRIM,
    inputTypes: ["video", "audio"],
    outputTypes: ["video", "audio"],
    preview: true,
    order: 40,
    params: [
      {
        key: "segments",
        type: "string", // 特殊类型，实际上是数组
        nameKey: "actions.exclude_segment.params.segments",
        required: true,
        defaultValue: [],
      },
    ],
    validate: (params) => {
      const errors = [];
      if (
        !params.segments ||
        !Array.isArray(params.segments) ||
        params.segments.length === 0
      ) {
        errors.push("actions.exclude_segment.errors.no_segments");
      }
      return {
        isValid: errors.length === 0,
        errors,
      };
    },
    invokeCommand: "exclude_and_concat_segments",
    buildParams: (params: ActionParams, context: ActionContext) => {
      // 解析时间字符串 (HH:MM:SS)
      function timeStringToSeconds(timeStr: string): number {
        const parts = timeStr.split(":");
        const hours = parseInt(parts[0]) || 0;
        const minutes = parseInt(parts[1]) || 0;
        const seconds = parseInt(parts[2]) || 0;
        return hours * 3600 + minutes * 60 + seconds;
      }

      // 转换片段格式为后端需要的格式
      const segmentTuples = params.segments.map((segment: any) => [
        timeStringToSeconds(segment.startTimeStr),
        timeStringToSeconds(segment.endTimeStr),
      ]);

      return {
        inputPath: context.inputPath,
        outputPath: context.outputPath,
        segments: segmentTuples,
      };
    },
  },
  {
    id: ActionIds.CROP_VIDEO,
    nameKey: "actions.crop_video.name",
    descriptionKey: "actions.crop_video.description",
    categoryId: CategoryIds.TRIM,
    inputTypes: ["video"],
    outputTypes: ["video"],
    preview: true,
    order: 50,
    params: [
      {
        key: "x",
        type: "number",
        nameKey: "actions.crop_video.params.x",
        required: true,
        defaultValue: 0,
        min: 0,
      },
      {
        key: "y",
        type: "number",
        nameKey: "actions.crop_video.params.y",
        required: true,
        defaultValue: 0,
        min: 0,
      },
      {
        key: "width",
        type: "number",
        nameKey: "actions.crop_video.params.width",
        required: true,
        defaultValue: 640,
        min: 1,
      },
      {
        key: "height",
        type: "number",
        nameKey: "actions.crop_video.params.height",
        required: true,
        defaultValue: 480,
        min: 1,
      },
    ],
    validate: (params) => {
      const errors = [];

      const x = parseInt(params.x);
      if (isNaN(x)) {
        errors.push("actions.crop_video.errors.invalid_x_number");
      } else if (x < 0) {
        errors.push("actions.crop_video.errors.negative_x");
      }

      const y = parseInt(params.y);
      if (isNaN(y)) {
        errors.push("actions.crop_video.errors.invalid_y_number");
      } else if (y < 0) {
        errors.push("actions.crop_video.errors.negative_y");
      }

      const width = parseInt(params.width);
      if (isNaN(width)) {
        errors.push("actions.crop_video.errors.invalid_width_number");
      } else if (width <= 0) {
        errors.push("actions.crop_video.errors.invalid_width");
      }

      const height = parseInt(params.height);
      if (isNaN(height)) {
        errors.push("actions.crop_video.errors.invalid_height_number");
      } else if (height <= 0) {
        errors.push("actions.crop_video.errors.invalid_height");
      }

      return {
        isValid: errors.length === 0,
        errors,
      };
    },
    invokeCommand: "crop_video",
    buildParams: (params: ActionParams, context: ActionContext) => ({
      inputPath: context.inputPath,
      outputPath: context.outputPath,
      x: parseInt(params.x) || 0,
      y: parseInt(params.y) || 0,
      width: parseInt(params.width) || 640,
      height: parseInt(params.height) || 480,
    }),
  },
];
