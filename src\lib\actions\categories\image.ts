// 图片相关动作定义

import {
  CategoryIds,
  ActionIds,
  ImageFormats,
  ResolutionPresets,
} from "../../constants";
import type { Action } from "../../types/action";
import { applyActionMapping } from "../action-mappings";

// 图片相关动作定义
const rawImageActions = [
  {
    id: ActionIds.ADD_COVER_IMAGE,
    nameKey: "actions.add_cover_image.name",
    descriptionKey: "actions.add_cover_image.description",
    categoryId: CategoryIds.IMAGE,
    inputTypes: ["video"],
    outputTypes: ["video"],
    preview: true,
    order: 10,
    params: [
      {
        key: "imagePath",
        type: "image-file",
        nameKey: "actions.add_cover_image.params.imagePath",
        required: true,
        defaultValue: "",
      },
      {
        key: "duration",
        type: "duration",
        nameKey: "actions.add_cover_image.params.duration",
        required: true,
        defaultValue: 3.0,
        min: 0.1,
        max: 60.0,
        step: 0.1,
      },
    ],
    validate: (params: Record<string, any>) => {
      const errors = [];
      if (!params.imagePath || params.imagePath.trim() === "") {
        errors.push("actions.add_cover_image.errors.no_image_file");
      }

      const duration = parseFloat(params.duration);
      if (isNaN(duration)) {
        errors.push("actions.add_cover_image.errors.invalid_duration_number");
      } else if (duration < 0.1 || duration > 60.0) {
        errors.push("actions.add_cover_image.errors.invalid_duration");
      }

      return {
        isValid: errors.length === 0,
        errors,
      };
    },
  },
  {
    id: ActionIds.ADD_END_IMAGE,
    nameKey: "actions.add_end_image.name",
    descriptionKey: "actions.add_end_image.description",
    categoryId: CategoryIds.IMAGE,
    inputTypes: ["video"],
    outputTypes: ["video"],
    preview: true,
    order: 20,
    params: [
      {
        key: "imagePath",
        type: "image-file",
        nameKey: "actions.add_end_image.params.imagePath",
        required: true,
        defaultValue: "",
      },
      {
        key: "duration",
        type: "duration",
        nameKey: "actions.add_end_image.params.duration",
        required: true,
        defaultValue: 3.0,
        min: 0.1,
        max: 60.0,
        step: 0.1,
      },
    ],
    validate: (params: Record<string, any>) => {
      const errors = [];
      if (!params.imagePath || params.imagePath.trim() === "") {
        errors.push("actions.add_end_image.errors.no_image_file");
      }

      const duration = parseFloat(params.duration);
      if (isNaN(duration)) {
        errors.push("actions.add_end_image.errors.invalid_duration_number");
      } else if (duration < 0.1 || duration > 60.0) {
        errors.push("actions.add_end_image.errors.invalid_duration");
      }

      return {
        isValid: errors.length === 0,
        errors,
      };
    },
  },
  {
    id: ActionIds.IMAGE_TO_VIDEO,
    nameKey: "actions.image_to_video.name",
    descriptionKey: "actions.image_to_video.description",
    categoryId: CategoryIds.IMAGE,
    inputTypes: ["image"],
    outputTypes: ["video"],
    preview: true,
    order: 30,
    params: [
      {
        key: "duration",
        type: "duration",
        nameKey: "actions.image_to_video.params.duration",
        required: true,
        defaultValue: 5.0,
        min: 0.1,
        max: 3600.0,
        step: 0.1,
      },
      {
        key: "resolution",
        type: "select",
        nameKey: "actions.image_to_video.params.resolution",
        required: true,
        defaultValue: "720p",
        options: [
          { value: "480p", labelKey: "resolutions.480p" },
          { value: "720p", labelKey: "resolutions.720p" },
          { value: "1080p", labelKey: "resolutions.1080p" },
          { value: "custom", labelKey: "resolutions.custom" },
        ],
      },
      {
        key: "customWidth",
        type: "number",
        nameKey: "actions.image_to_video.params.customWidth",
        required: false,
        defaultValue: 1280,
        min: 64,
        max: 7680,
      },
      {
        key: "customHeight",
        type: "number",
        nameKey: "actions.image_to_video.params.customHeight",
        required: false,
        defaultValue: 720,
        min: 64,
        max: 4320,
      },
    ],
    validate: (params: Record<string, any>) => {
      const errors = [];

      const duration = parseFloat(params.duration);
      if (isNaN(duration)) {
        errors.push("actions.image_to_video.errors.invalid_duration_number");
      } else if (duration < 0.1 || duration > 3600.0) {
        errors.push("actions.image_to_video.errors.invalid_duration");
      }

      if (params.resolution === "custom") {
        const customWidth = parseInt(params.customWidth);
        if (isNaN(customWidth)) {
          errors.push("actions.image_to_video.errors.invalid_width_number");
        } else if (customWidth < 64 || customWidth > 7680) {
          errors.push("actions.image_to_video.errors.invalid_width");
        }

        const customHeight = parseInt(params.customHeight);
        if (isNaN(customHeight)) {
          errors.push("actions.image_to_video.errors.invalid_height_number");
        } else if (customHeight < 64 || customHeight > 4320) {
          errors.push("actions.image_to_video.errors.invalid_height");
        }
      }

      return {
        isValid: errors.length === 0,
        errors,
      };
    },
  },
  {
    id: ActionIds.BATCH_IMAGE_TO_VIDEO,
    nameKey: "actions.batch_image_to_video.name",
    descriptionKey: "actions.batch_image_to_video.description",
    categoryId: CategoryIds.IMAGE,
    inputTypes: ["folder"],
    outputTypes: ["video"],
    preview: true,
    order: 40,
    params: [
      {
        key: "durationMode",
        type: "select",
        nameKey: "actions.batch_image_to_video.params.durationMode",
        required: true,
        defaultValue: "per_image",
        options: [
          { value: "per_image", labelKey: "duration_modes.per_image" },
          { value: "total", labelKey: "duration_modes.total" },
        ],
      },
      {
        key: "durationValue",
        type: "duration",
        nameKey: "actions.batch_image_to_video.params.durationValue",
        required: true,
        defaultValue: 3.0,
        min: 0.1,
        max: 300.0,
        step: 0.1,
      },
      {
        key: "resolution",
        type: "select",
        nameKey: "actions.batch_image_to_video.params.resolution",
        required: true,
        defaultValue: "720p",
        options: [
          { value: "480p", labelKey: "resolutions.480p" },
          { value: "720p", labelKey: "resolutions.720p" },
          { value: "1080p", labelKey: "resolutions.1080p" },
          { value: "custom", labelKey: "resolutions.custom" },
        ],
      },
      {
        key: "customWidth",
        type: "number",
        nameKey: "actions.batch_image_to_video.params.customWidth",
        required: false,
        defaultValue: 1280,
        min: 64,
        max: 7680,
      },
      {
        key: "customHeight",
        type: "number",
        nameKey: "actions.batch_image_to_video.params.customHeight",
        required: false,
        defaultValue: 720,
        min: 64,
        max: 4320,
      },
      {
        key: "transitionType",
        type: "select",
        nameKey: "actions.batch_image_to_video.params.transitionType",
        required: true,
        defaultValue: "none",
        options: [
          { value: "none", labelKey: "transitions.none" },
          { value: "fade", labelKey: "transitions.fade" },
          { value: "slideLeft", labelKey: "transitions.slideLeft" },
          { value: "slideRight", labelKey: "transitions.slideRight" },
        ],
      },
      {
        key: "transitionDuration",
        type: "duration",
        nameKey: "actions.batch_image_to_video.params.transitionDuration",
        required: false,
        defaultValue: 0.5,
        min: 0.1,
        max: 5.0,
        step: 0.1,
      },
    ],
    validate: (params: Record<string, any>) => {
      const errors = [];

      const durationValue = parseFloat(params.durationValue);
      if (isNaN(durationValue)) {
        errors.push(
          "actions.batch_image_to_video.errors.invalid_duration_number"
        );
      } else if (durationValue < 0.1 || durationValue > 3600.0) {
        errors.push("actions.batch_image_to_video.errors.invalid_duration");
      }

      if (params.transitionType !== "none") {
        const transitionDuration = parseFloat(params.transitionDuration);
        if (isNaN(transitionDuration)) {
          errors.push(
            "actions.batch_image_to_video.errors.invalid_transition_duration_number"
          );
        } else if (transitionDuration < 0.1 || transitionDuration > 5.0) {
          errors.push(
            "actions.batch_image_to_video.errors.invalid_transition_duration"
          );
        }
      }

      // 验证自定义分辨率
      if (params.resolution === "custom") {
        const customWidth = parseInt(params.customWidth);
        if (isNaN(customWidth)) {
          errors.push(
            "actions.batch_image_to_video.errors.invalid_width_number"
          );
        } else if (customWidth < 64 || customWidth > 7680) {
          errors.push("actions.batch_image_to_video.errors.invalid_width");
        }

        const customHeight = parseInt(params.customHeight);
        if (isNaN(customHeight)) {
          errors.push(
            "actions.batch_image_to_video.errors.invalid_height_number"
          );
        } else if (customHeight < 64 || customHeight > 4320) {
          errors.push("actions.batch_image_to_video.errors.invalid_height");
        }
      }

      return {
        isValid: errors.length === 0,
        errors,
      };
    },
  },
  {
    id: ActionIds.VIDEO_TO_IMAGE,
    nameKey: "actions.video_to_image.name",
    descriptionKey: "actions.video_to_image.description",
    categoryId: CategoryIds.IMAGE,
    inputTypes: ["video"],
    outputTypes: ["image"],
    preview: false,
    order: 50,
    params: [
      {
        key: "count",
        type: "number",
        nameKey: "actions.video_to_image.params.count",
        required: true,
        defaultValue: 5,
        min: 1,
        max: 1000,
      },
      {
        key: "format",
        type: "select",
        nameKey: "actions.video_to_image.params.format",
        required: true,
        defaultValue: ImageFormats.JPG,
        options: [
          { value: ImageFormats.JPG, labelKey: "formats.image.jpg" },
          { value: ImageFormats.PNG, labelKey: "formats.image.png" },
          { value: ImageFormats.WEBP, labelKey: "formats.image.webp" },
        ],
      },
      {
        key: "method",
        type: "select",
        nameKey: "actions.video_to_image.params.method",
        required: true,
        defaultValue: "uniform",
        options: [
          { value: "uniform", labelKey: "extraction_methods.uniform" },
          { value: "random", labelKey: "extraction_methods.random" },
        ],
      },
      {
        key: "quality",
        type: "range",
        nameKey: "actions.video_to_image.params.quality",
        required: true,
        defaultValue: 85,
        min: 1,
        max: 100,
        step: 1,
      },
      {
        key: "useCustomSize",
        type: "checkbox",
        nameKey: "actions.video_to_image.params.useCustomSize",
        required: false,
        defaultValue: false,
      },
      {
        key: "customWidth",
        type: "number",
        nameKey: "actions.video_to_image.params.customWidth",
        required: false,
        defaultValue: 1920,
        min: 1,
        max: 7680,
        dependsOn: "useCustomSize",
      },
    ],
    validate: (params: Record<string, any>) => {
      const errors = [];
      if (params.count < 1 || params.count > 1000) {
        errors.push("actions.video_to_image.errors.invalid_count");
      }
      if (params.quality < 1 || params.quality > 100) {
        errors.push("actions.video_to_image.errors.invalid_quality");
      }
      return {
        isValid: errors.length === 0,
        errors,
      };
    },
  },
  {
    id: ActionIds.VIDEO_TO_GIF,
    nameKey: "actions.video_to_gif.name",
    descriptionKey: "actions.video_to_gif.description",
    categoryId: CategoryIds.IMAGE,
    inputTypes: ["video"],
    outputTypes: ["image"],
    preview: true,
    order: 60,
    params: [
      {
        key: "format",
        type: "select",
        nameKey: "actions.video_to_gif.params.format",
        required: true,
        defaultValue: "gif",
        options: [
          { value: "gif", labelKey: "formats.image.gif" },
          { value: "webp", labelKey: "formats.image.webp" },
        ],
      },
      {
        key: "startTime",
        type: "duration",
        nameKey: "actions.video_to_gif.params.startTime",
        required: true,
        defaultValue: 0,
        min: 0,
      },
      {
        key: "duration",
        type: "duration",
        nameKey: "actions.video_to_gif.params.duration",
        required: true,
        defaultValue: 10,
        min: 0.1,
        max: 300,
        step: 0.1,
      },
      {
        key: "fps",
        type: "range",
        nameKey: "actions.video_to_gif.params.fps",
        required: true,
        defaultValue: 10,
        min: 1,
        max: 30,
        step: 1,
      },
      {
        key: "quality",
        type: "range",
        nameKey: "actions.video_to_gif.params.quality",
        required: true,
        defaultValue: 80,
        min: 1,
        max: 100,
        step: 1,
      },
      {
        key: "loopCount",
        type: "number",
        nameKey: "actions.video_to_gif.params.loopCount",
        required: true,
        defaultValue: -1,
        min: -1,
        max: 100,
      },
    ],
    validate: (params: Record<string, any>) => {
      const errors = [];
      if (params.startTime < 0) {
        errors.push("actions.video_to_gif.errors.negative_start_time");
      }
      if (params.duration < 0.1 || params.duration > 300) {
        errors.push("actions.video_to_gif.errors.invalid_duration");
      }
      if (params.fps < 1 || params.fps > 30) {
        errors.push("actions.video_to_gif.errors.invalid_fps");
      }
      if (params.quality < 1 || params.quality > 100) {
        errors.push("actions.video_to_gif.errors.invalid_quality");
      }
      return {
        isValid: errors.length === 0,
        errors,
      };
    },
  },
];

// 应用映射并导出
export const imageActions: Action[] = rawImageActions.map(applyActionMapping);
