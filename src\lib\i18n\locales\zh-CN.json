{"app": {"title": "VideoIDE", "description": "视频处理工具"}, "common": {"add": "添加", "remove": "删除", "edit": "编辑", "save": "保存", "cancel": "取消", "confirm": "确认", "close": "关闭", "search": "搜索", "preview": "预览", "settings": "设置", "loading": "加载中...", "processing": "处理中...", "completed": "完成", "error": "错误", "warning": "警告", "info": "信息", "success": "成功", "browse": "浏览", "select": "选择", "upload": "上传", "download": "下载", "import": "导入", "export": "导出", "clear": "清空", "reset": "重置", "apply": "应用", "undo": "撤销", "redo": "重做", "copy": "复制", "paste": "粘贴", "cut": "剪切", "move_up": "上移", "move_down": "下移", "expand": "展开", "collapse": "折叠", "sort": "排序", "filter": "过滤", "refresh": "刷新", "help": "帮助", "about": "关于"}, "categories": {"trim": {"name": "裁剪", "description": "视频和音频的时间裁剪操作"}, "color": {"name": "颜色调整", "description": "视频颜色、亮度、对比度等调整"}, "audio": {"name": "音频处理", "description": "音频相关的处理操作"}, "transform": {"name": "变换", "description": "视频的旋转、缩放、裁剪等变换操作"}, "effect": {"name": "特效", "description": "各种视觉特效和滤镜"}, "watermark": {"name": "水印", "description": "添加文字或图片水印"}, "encode": {"name": "编码", "description": "视频格式转换和编码设置"}, "image": {"name": "图片", "description": "图片和视频之间的转换操作"}, "filter": {"name": "滤镜", "description": "各种视频滤镜效果"}}, "actions": {"trim_start": {"name": "截掉头部", "description": "从视频开头截掉指定时长", "params": {"startDuration": "头部时长(秒)"}, "errors": {"negative_duration": "时长不能为负数", "invalid_number": "请输入有效的数字"}}, "trim_end": {"name": "截掉尾部", "description": "从视频结尾截掉指定时长", "params": {"endTime": "尾部时长(秒)"}, "errors": {"negative_duration": "时长不能为负数", "invalid_number": "请输入有效的数字"}}, "trim_segment": {"name": "截取多片段合并", "description": "截取视频多个片段并合并", "params": {"segments": "时间片段"}, "errors": {"no_segments": "请至少添加一个时间片段"}}, "exclude_segment": {"name": "剔除多片段后合并", "description": "剔除指定片段，合并剩余部分", "params": {"segments": "要剔除的时间片段"}, "errors": {"no_segments": "请至少添加一个要剔除的时间片段"}}, "crop_video": {"name": "裁剪画面", "description": "裁剪视频画面尺寸", "params": {"x": "X坐标", "y": "Y坐标", "width": "宽度", "height": "高度"}, "errors": {"negative_x": "X坐标不能为负数", "negative_y": "Y坐标不能为负数", "invalid_width": "宽度必须大于0", "invalid_height": "高度必须大于0", "invalid_x_number": "请输入有效的X坐标数字", "invalid_y_number": "请输入有效的Y坐标数字", "invalid_width_number": "请输入有效的宽度数字", "invalid_height_number": "请输入有效的高度数字"}}, "adjust_brightness": {"name": "调整亮度", "description": "调整视频的亮度", "params": {"brightness": "亮度值"}, "errors": {"invalid_range": "亮度值必须在-1到1之间", "invalid_number": "请输入有效的数字"}}, "adjust_contrast": {"name": "调整对比度", "description": "调整视频的对比度", "params": {"contrast": "对比度值"}, "errors": {"invalid_range": "对比度值必须在0到3之间"}}, "adjust_saturation": {"name": "调整饱和度", "description": "调整视频的饱和度", "params": {"saturation": "饱和度值"}, "errors": {"invalid_range": "饱和度值必须在0到3之间"}}, "adjust_hue": {"name": "调整色相", "description": "调整视频的色相", "params": {"hue": "色相值"}, "errors": {"invalid_range": "色相值必须在-180到180之间"}}, "adjust_gamma": {"name": "调整伽马值", "description": "调整视频的伽马值", "params": {"gamma": "伽马值"}, "errors": {"invalid_range": "伽马值必须在0.1到3.0之间"}}, "white_balance": {"name": "白平衡", "description": "调整视频的白平衡", "params": {"temperature": "色温值"}, "errors": {"invalid_range": "色温值必须在2000到11000之间"}}, "adjust_volume": {"name": "调整音量", "description": "调整音频的音量大小", "params": {"volume": "音量倍数"}, "errors": {"invalid_range": "音量倍数必须在0到3之间", "invalid_number": "请输入有效的数字"}}, "extract_audio": {"name": "提取音频", "description": "从视频中提取音频", "params": {"format": "音频格式"}, "errors": {"invalid_format": "不支持的音频格式"}}, "add_background_music": {"name": "添加背景音乐", "description": "为视频添加背景音乐", "params": {"audioFile": "音频文件", "loopBgm": "循环播放", "bgmVolume": "背景音乐音量"}, "errors": {"no_audio_file": "请选择音频文件", "invalid_volume": "音量值无效", "invalid_volume_number": "请输入有效的音量数字"}}, "replace_audio": {"name": "替换音频", "description": "替换视频的音频轨道", "params": {"audioFile": "音频文件", "loopAudio": "循环播放", "audioVolume": "音频音量"}, "errors": {"no_audio_file": "请选择音频文件", "invalid_volume": "音量值无效", "invalid_volume_number": "请输入有效的音量数字"}}, "mute_audio": {"name": "静音", "description": "移除视频的音频", "params": {}, "errors": {}}, "reverse_video": {"name": "反转视频", "description": "将视频倒序播放", "params": {"keepAudio": "保留音频"}}, "rotate_video": {"name": "旋转视频", "description": "旋转视频画面", "params": {"angle": "旋转角度", "customAngle": "自定义角度"}, "options": {"90": "90度", "180": "180度", "270": "270度", "custom": "自定义"}, "errors": {"invalid_custom_angle": "自定义角度必须在-360到360之间"}}, "scale_video": {"name": "缩放视频", "description": "缩放视频尺寸", "params": {"scale": "缩放倍数"}, "errors": {"invalid_scale": "缩放倍数必须在0.1到5.0之间"}}, "flip_horizontal": {"name": "水平翻转", "description": "水平翻转视频画面", "params": {}, "errors": {}}, "flip_vertical": {"name": "垂直翻转", "description": "垂直翻转视频画面", "params": {}, "errors": {}}, "adjust_speed": {"name": "调整播放速度", "description": "调整视频播放速度", "params": {"speed": "播放速度"}, "errors": {"invalid_speed": "播放速度必须在0.1到10.0之间"}}, "fade_in": {"name": "淡入效果", "description": "添加淡入效果", "params": {"duration": "淡入时长"}, "errors": {"invalid_duration": "时长必须在0.1到10.0秒之间"}}, "fade_out": {"name": "淡出效果", "description": "添加淡出效果", "params": {"duration": "淡出时长"}, "errors": {"invalid_duration": "时长必须在0.1到10.0秒之间"}}, "blur_effect": {"name": "模糊效果", "description": "添加模糊效果", "params": {"intensity": "模糊强度"}, "errors": {"invalid_intensity": "模糊强度必须在1到20之间"}}, "sharpen_effect": {"name": "锐化效果", "description": "添加锐化效果", "params": {"intensity": "锐化强度"}, "errors": {"invalid_intensity": "锐化强度必须在0.1到3.0之间"}}, "grayscale": {"name": "灰度", "description": "将视频转为灰度"}, "sepia": {"name": "怀旧", "description": "添加怀旧色调效果"}, "emboss": {"name": "浮雕", "description": "添加浮雕效果"}, "sketch": {"name": "素描", "description": "添加素描效果", "params": {"mode": "模式", "mode_options": {"gray": "灰度", "color": "彩色"}}}, "oil_painting": {"name": "油画", "description": "添加油画效果", "params": {"intensity": "强度"}}, "pixelate": {"name": "像素化", "description": "添加像素化效果", "params": {"pixelSize": "像素大小"}}, "edge_detection": {"name": "边缘检测", "description": "检测并突出边缘"}, "vintage_filter": {"name": "复古滤镜", "description": "添加复古效果"}, "cold_tone": {"name": "冷色调", "description": "添加冷色调效果", "params": {"strength": "强度"}}, "warm_tone": {"name": "暖色调", "description": "添加暖色调效果", "params": {"strength": "强度"}}, "mosaic": {"name": "马赛克", "description": "添加马赛克效果", "params": {"blockSize": "方块大小"}, "errors": {"invalid_block_size": "方块大小必须在2到50之间"}}, "text_watermark": {"name": "文字水印", "description": "添加文字水印", "params": {"text": "水印文字", "position": "位置", "fontSize": "字体大小", "fontColor": "字体颜色", "opacity": "透明度", "customX": "自定义X坐标", "customY": "自定义Y坐标"}, "errors": {"empty_text": "水印文字不能为空", "invalid_font_size": "字体大小必须在8到72之间", "invalid_opacity": "透明度必须在0.1到1.0之间"}}, "image_watermark": {"name": "图片水印", "description": "添加图片水印", "params": {"watermarkPath": "水印图片", "position": "位置", "width": "宽度", "opacity": "透明度", "customX": "自定义X坐标", "customY": "自定义Y坐标"}, "errors": {"no_watermark_file": "请选择水印图片", "invalid_width": "宽度必须在10到1000之间", "invalid_opacity": "透明度必须在0.1到1.0之间"}}, "video_watermark": {"name": "视频水印", "description": "添加视频水印", "params": {"watermarkPath": "水印视频", "position": "位置", "width": "宽度", "opacity": "透明度", "customX": "自定义X坐标", "customY": "自定义Y坐标"}, "errors": {"no_watermark_file": "请选择水印视频", "invalid_width": "宽度必须在10到1000之间", "invalid_opacity": "透明度必须在0.1到1.0之间", "invalid_width_number": "请输入有效的宽度数字", "invalid_opacity_number": "请输入有效的透明度数字"}}, "convert_format": {"name": "转换格式", "description": "转换视频格式", "params": {"format": "目标格式"}, "errors": {"invalid_format": "不支持的视频格式"}}, "compress_video": {"name": "压缩视频", "description": "压缩视频文件大小", "params": {"compressionLevel": "压缩级别"}, "errors": {"invalid_level": "压缩级别必须在1到10之间"}}, "adjust_bitrate": {"name": "调整码率", "description": "调整视频码率", "params": {"bitrateType": "码率类型", "customBitrate": "自定义码率"}, "errors": {"invalid_custom_bitrate": "自定义码率必须在100到50000之间"}}, "adjust_resolution": {"name": "调整分辨率", "description": "调整视频分辨率", "params": {"resolutionType": "分辨率类型", "customWidth": "自定义宽度", "customHeight": "自定义高度"}, "errors": {"invalid_width": "宽度必须在64到7680之间", "invalid_height": "高度必须在64到4320之间"}}, "add_cover_image": {"name": "添加封面图片", "description": "在视频开头添加封面图片", "params": {"imagePath": "封面图片文件", "duration": "显示时长"}, "errors": {"no_image_file": "请选择图片文件", "invalid_duration": "显示时长必须在0.1到60.0秒之间", "invalid_duration_number": "请输入有效的时长数字"}}, "add_end_image": {"name": "添加封底图片", "description": "在视频结尾添加封底图片", "params": {"imagePath": "封底图片文件", "duration": "显示时长"}, "errors": {"no_image_file": "请选择图片文件", "invalid_duration": "显示时长必须在0.1到60.0秒之间", "invalid_duration_number": "请输入有效的时长数字"}}, "image_to_video": {"name": "图片转视频", "description": "将图片转换为视频", "params": {"duration": "视频时长", "resolution": "分辨率", "customWidth": "自定义宽度", "customHeight": "自定义高度"}, "errors": {"invalid_duration": "视频时长必须在0.1到3600.0秒之间", "invalid_width": "宽度必须在64到7680之间", "invalid_height": "高度必须在64到4320之间", "invalid_duration_number": "请输入有效的时长数字", "invalid_width_number": "请输入有效的宽度数字", "invalid_height_number": "请输入有效的高度数字"}}, "batch_image_to_video": {"name": "批量图片转视频", "description": "将多张图片转换为视频", "params": {"durationMode": "时长模式", "durationValue": "时长值", "resolution": "分辨率", "customWidth": "自定义宽度", "customHeight": "自定义高度", "transitionType": "切换效果", "transitionDuration": "切换时长"}, "errors": {"invalid_duration": "时长值必须在0.1到300.0秒之间", "invalid_transition_duration": "切换时长必须在0.1到5.0秒之间", "invalid_duration_number": "请输入有效的时长数字", "invalid_transition_duration_number": "请输入有效的切换时长数字", "invalid_width": "宽度必须在64到7680之间", "invalid_height": "高度必须在64到4320之间", "invalid_width_number": "请输入有效的宽度数字", "invalid_height_number": "请输入有效的高度数字"}}, "video_to_image": {"name": "视频生图", "description": "从视频中提取图片", "params": {"count": "图片数量", "format": "图片格式", "method": "提取方式", "quality": "图片质量"}, "errors": {"invalid_count": "图片数量必须在1到1000之间", "invalid_quality": "图片质量必须在1到100之间"}}, "video_to_gif": {"name": "视频转动图", "description": "将视频转换为GIF或WebP动图", "params": {"format": "输出格式", "startTime": "开始时间", "duration": "持续时长", "fps": "帧率", "quality": "图片质量", "loopCount": "播放次数"}, "errors": {"negative_start_time": "开始时间不能为负数", "invalid_duration": "持续时长必须在0.1到300秒之间", "invalid_fps": "帧率必须在1到30之间", "invalid_quality": "图片质量必须在1到100之间"}}}, "formats": {"video": {"mp4": "MP4", "avi": "AVI", "mov": "MOV", "mkv": "MKV", "webm": "WebM"}, "audio": {"mp3": "MP3", "wav": "WAV", "aac": "AAC", "flac": "FLAC", "ogg": "OGG", "m4a": "M4A"}, "image": {"jpg": "JPG", "png": "PNG", "webp": "WebP", "gif": "GIF"}}, "resolutions": {"480p": "480p (854×480)", "720p": "720p (1280×720)", "1080p": "1080p (1920×1080)", "1440p": "1440p (2560×1440)", "4k": "4K (3840×2160)", "custom": "自定义"}, "positions": {"top_left": "左上", "top_right": "右上", "bottom_left": "左下", "bottom_right": "右下", "center": "中心", "custom": "自定义"}, "duration_modes": {"per_image": "每张图片", "total": "总时长"}, "transitions": {"none": "无效果", "fade": "淡入淡出", "slideLeft": "左翻页", "slideRight": "右翻页"}, "extraction_methods": {"uniform": "均匀提取", "keyframes": "关键帧"}, "bitrates": {"low": "低码率", "medium": "中等码率", "high": "高码率", "ultra": "超高码率", "custom": "自定义码率"}, "ui": {"action_list": {"search_placeholder": "搜索动作...", "search_results": "搜索结果", "no_results": "未找到匹配的动作", "category": "类别", "added": "已添加", "theme": "主题"}, "action_card": {"description": "描述", "description_placeholder": "动作描述...", "segments": "片段", "no_segments": "暂无片段", "segments_hint": "请使用预览功能导入时间片段", "segments_imported": "从预览导入: {count}个片段", "time_overlap": "时间有重叠", "delete_all": "删除全部", "validation_errors": "参数验证错误:"}, "processing": {"title": "处理中...", "cancelled": "处理已取消", "completed": "所有动作处理完成！", "progress": "进度"}, "language": {"switch": "切换语言", "current": "当前语言"}}, "messages": {"file_select_error": "文件选择失败", "validation_failed": "参数验证失败", "action_added": "动作已添加", "action_removed": "动作已删除", "language_switched": "语言已切换到 {language}"}}