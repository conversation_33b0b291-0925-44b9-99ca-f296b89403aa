<!-- 重构后的动作卡片组件 -->
<script lang="ts">
  import { Card, CardHeader, CardContent } from "$lib/components/ui/card";
  import { getContext } from "svelte";
  import { registry } from "$lib/actions/registry";

  import ActionParamInput from "$lib/components/ui/action-param-input.svelte";
  import type { ActionInstance, Action } from "$lib/types/action";
  import { ActionIds } from "$lib/constants";

  interface Props {
    action: ActionInstance;
    onRemove: (id: string) => void;
    onMoveUp: (id: string) => void;
    onMoveDown: (id: string) => void;
    onToggleCollapse: (id: string) => void;
    onUpdateParams: (id: string, params: any) => void;

    t?: (key: string) => string; // i18n翻译函数
  }

  let {
    action,
    onRemove,
    onMoveUp,
    onMoveDown,
    onToggleCollapse,
    onUpdateParams,
    t = (key: string) => key,
  }: Props = $props();

  // 获取视频选择组件实例
  let videoSelectorComponent: any = null;
  try {
    videoSelectorComponent = getContext("videoSelectorComponent");
  } catch {}

  // 获取动作定义
  function getActionDefinition(): Action | undefined {
    return registry.getAction(action.actionId);
  }

  // 参数值变化处理
  function handleParamChange(paramKey: string, value: any): void {
    const newParams = { ...action.params, [paramKey]: value };
    onUpdateParams(action.id, newParams);
  }

  // 打开视频预览
  function openVideoPreview(): void {
    if (videoSelectorComponent?.openPreview) {
      videoSelectorComponent.openPreview(action.id);
    }
  }

  // 检查是否支持预览
  function supportsPreview(): boolean {
    const actionDef = getActionDefinition();
    return actionDef?.preview === true;
  }

  // 检查是否为片段相关动作
  function isSegmentAction(): boolean {
    return (
      action.actionId === ActionIds.TRIM_SEGMENT ||
      action.actionId === ActionIds.EXCLUDE_SEGMENT
    );
  }

  // 检查片段重叠
  function detectOverlaps(segments: any[]): boolean {
    if (!segments || segments.length < 2) return false;

    for (let i = 0; i < segments.length - 1; i++) {
      for (let j = i + 1; j < segments.length; j++) {
        const seg1 = segments[i];
        const seg2 = segments[j];

        if (seg1.start < seg2.end && seg2.start < seg1.end) {
          return true;
        }
      }
    }
    return false;
  }

  // 检查是否应该显示参数
  function shouldShowParam(paramDef: any): boolean {
    // 处理dependsOn属性（优先级最高）
    if (paramDef.dependsOn) {
      const dependentValue = action.params[paramDef.dependsOn];
      const shouldShow = Boolean(dependentValue);
      console.log(
        `shouldShowParam: ${paramDef.key} depends on ${paramDef.dependsOn} = ${dependentValue}, shouldShow = ${shouldShow}`
      );
      // 如果依赖的参数值为true（对于checkbox/boolean类型）或非空值，则显示
      return shouldShow;
    }

    // 对于自定义分辨率字段，只有当resolution选择为custom时才显示（兼容旧逻辑）
    if (paramDef.key === "customWidth" || paramDef.key === "customHeight") {
      return action.params.resolution === "custom";
    }

    return true;
  }

  // 排序片段
  function sortSegments(): void {
    if (action.params.segments && Array.isArray(action.params.segments)) {
      const sorted = [...action.params.segments].sort(
        (a, b) => a.start - b.start
      );
      handleParamChange("segments", sorted);
    }
  }

  // 删除片段
  function deleteSegment(index: number): void {
    if (action.params.segments && Array.isArray(action.params.segments)) {
      const newSegments = action.params.segments.filter((_, i) => i !== index);
      handleParamChange("segments", newSegments);
    }
  }

  // 删除所有片段
  function deleteAllSegments(): void {
    handleParamChange("segments", []);
  }

  // 格式化时间显示
  function formatTime(seconds: number): string {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, "0")}`;
  }
</script>

<Card class="w-full">
  <CardHeader>
    <div class="flex items-center justify-between">
      <div class="flex items-center space-x-2">
        <div
          class="w-6 h-6 flex items-center justify-center text-muted-foreground cursor-move hover:text-foreground select-none"
        >
          ⋮⋮
        </div>
        <h3 class="text-lg font-semibold">{action.name}</h3>
        <button
          class="px-2 py-1 text-sm text-muted-foreground hover:text-foreground"
          onclick={() => onToggleCollapse(action.id)}
        >
          {action.isCollapsed ? "展开" : "折叠"}
        </button>
      </div>
      <div class="flex items-center space-x-1">
        {#if supportsPreview()}
          <button
            class="px-2 py-1 text-sm text-blue-600 hover:text-blue-700"
            onclick={openVideoPreview}
          >
            预览
          </button>
        {/if}
        <button
          class="px-2 py-1 text-sm text-muted-foreground hover:text-foreground"
          onclick={() => onMoveUp(action.id)}
        >
          上移
        </button>
        <button
          class="px-2 py-1 text-sm text-muted-foreground hover:text-foreground"
          onclick={() => onMoveDown(action.id)}
        >
          下移
        </button>
        <button
          class="px-2 py-1 text-sm text-red-600 hover:text-red-700"
          onclick={() => onRemove(action.id)}
        >
          删除
        </button>
      </div>
    </div>
  </CardHeader>

  {#if !action.isCollapsed}
    <CardContent class="space-y-4">
      <!-- 片段相关动作的特殊处理 -->
      {#if isSegmentAction()}
        <div class="space-y-3">
          <!-- 片段列表 -->
          {#if action.params.segments && action.params.segments.length > 0}
            <div class="space-y-2">
              <div class="flex items-center justify-between">
                <div class="flex items-center gap-2">
                  <span class="text-sm text-muted-foreground">
                    从预览导入: {action.params.segments.length}个片段
                  </span>
                  {#if detectOverlaps(action.params.segments)}
                    <span
                      class="text-xs text-red-600 bg-red-50 px-2 py-1 rounded"
                    >
                      时间有重叠
                    </span>
                  {/if}
                </div>
                <div class="flex items-center gap-1">
                  <button
                    class="px-2 py-1 text-xs text-blue-600 hover:text-blue-700 border border-blue-200 rounded"
                    onclick={sortSegments}
                  >
                    排序
                  </button>
                  <button
                    class="px-2 py-1 text-xs text-red-600 hover:text-red-700 border border-red-200 rounded"
                    onclick={deleteAllSegments}
                  >
                    删除全部
                  </button>
                </div>
              </div>

              <!-- 片段列表显示 -->
              <div class="max-h-32 overflow-y-auto space-y-1">
                {#each action.params.segments as segment, index}
                  <div
                    class="flex items-center justify-between p-2 bg-muted rounded text-sm"
                  >
                    <span>
                      {formatTime(segment.start)} - {formatTime(segment.end)}
                    </span>
                    <button
                      class="text-red-600 hover:text-red-700 text-xs"
                      onclick={() => deleteSegment(index)}
                    >
                      删除
                    </button>
                  </div>
                {/each}
              </div>
            </div>
          {:else}
            <div class="text-center text-muted-foreground py-4">
              <p class="text-sm">暂无片段</p>
              <p class="text-xs">请使用预览功能导入时间片段</p>
            </div>
          {/if}
        </div>
      {:else}
        <!-- 常规参数输入 -->
        {#each getActionDefinition()?.params || [] as paramDef}
          {#if shouldShowParam(paramDef)}
            <ActionParamInput
              param={paramDef}
              value={action.params[paramDef.key]}
              onValueChange={(value) => handleParamChange(paramDef.key, value)}
              {t}
            />
          {/if}
        {/each}
      {/if}
    </CardContent>
  {/if}
</Card>
