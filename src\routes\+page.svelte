<script lang="ts">
  import { invoke } from "@tauri-apps/api/core";
  import { basename, dirname, join } from "@tauri-apps/api/path";
  import { listen } from "@tauri-apps/api/event";
  import VideoSelector from "$lib/components/video-selector.svelte";
  import ActionList from "$lib/components/action-list.svelte";
  import ProcessingList from "$lib/components/processing-list.svelte";
  import ProcessingDialog from "$lib/components/processing-dialog.svelte";
  import ActionChainList from "$lib/components/action-chain-list.svelte";
  import { Tabs } from "$lib/components/ui/tabs";
  import { Input } from "$lib/components/ui/input";
  import { Button } from "$lib/components/ui/button";
  import { registry } from "$lib/actions";
  import { createDefaultParams } from "$lib/actions/utils";
  import type { ActionParams, ActionContext } from "$lib/types/action";
  import { t } from "$lib/i18n";
  import type { Action<PERSON>hainStatus, ActionData } from "$lib/types/action-chain";
  import type {
    ActionInstance,
    ActionTemplate,
    MediaType,
  } from "$lib/types/action";
  import {
    initActionChainManager,
    saveActionChain,
    loadActionChain,
    updateActionChainUsage,
  } from "$lib/utils/action-chain-api";
  import { setContext, onMount } from "svelte";
  import { theme } from "$lib/utils/theme";

  let actions: ActionInstance[] = $state([]);
  let nextId = $state(1);
  let videoSelectorComponent: any = $state(null);
  let actionListComponent: any;
  let showProgressDialog = $state(false);
  let currentProcessingAction = $state("");
  let currentActionIndex = $state(1);
  let totalActions = $state(1);
  let isAllCompleted = $state(false);
  let isCancelled = $state(false);
  let processingErrorMessage = $state("");
  let errorMessage = $state("");

  // Tab相关状态
  let activeTab = $state("processing");
  let tabs = [
    { id: "processing", label: "处理列表(Ctrl+1)" },
    { id: "chains", label: "动作链列表(Ctrl+2)" },
  ];

  // 动作链相关状态
  let actionChainStatus: ActionChainStatus = $state({
    state: "EMPTY",
    name: undefined,
    hasUnsavedChanges: false,
  });

  // 保存对话框状态
  let showSaveDialog = $state(false);
  let saveDialogName = $state("");
  let saveDialogDescription = $state("");
  let saveDialogMode: "save" | "saveAs" = $state("save");

  // 确认对话框状态
  let showConfirmDialog = $state(false);
  let confirmDialogTitle = $state("");
  let confirmDialogMessage = $state("");
  let confirmDialogAction: (() => void) | null = $state(null);

  // 文件选择状态（提升到主页面级别以保持Tab切换时的状态）
  let selectedFilePath: string | null = $state(null);
  let selectedFileInfo: any = $state(null);

  // 处理文件选择变化
  function handleFileChange(filePath: string | null, fileInfo: any) {
    selectedFilePath = filePath;
    selectedFileInfo = fileInfo;
    errorMessage = "";
  }

  // 显示确认对话框
  function showConfirm(title: string, message: string, action: () => void) {
    confirmDialogTitle = title;
    confirmDialogMessage = message;
    confirmDialogAction = action;
    showConfirmDialog = true;
  }

  // 确认对话框操作
  function confirmAction() {
    if (confirmDialogAction) {
      confirmDialogAction();
    }
    showConfirmDialog = false;
    confirmDialogAction = null;
  }

  function cancelConfirm() {
    showConfirmDialog = false;
    confirmDialogAction = null;
  }

  function addAction(actionTemplate: ActionTemplate) {
    // 检查是否已经存在相同名称的action
    const existingAction = actions.find(
      (action) => action.name === actionTemplate.name
    );
    if (existingAction) {
      // 可以显示一个提示，或者直接阻止添加
      console.warn(`Action "${actionTemplate.name}" 已经存在，不能重复添加`);
      return;
    }

    // 从动作注册系统获取默认参数
    const actionDef = registry.getAction(actionTemplate.id);
    const defaultParams = actionDef
      ? createDefaultParams(actionDef.params)
      : {};

    const newAction: ActionInstance = {
      id: `action-${nextId++}`,
      actionId: actionTemplate.id,
      name: actionTemplate.name,
      description: actionTemplate.description,
      params: defaultParams,
      isCollapsed: false,
      inputTypes: actionTemplate.inputTypes as MediaType[],
      outputTypes: actionTemplate.outputTypes as MediaType[],
    };
    actions = [...actions, newAction];

    // 标记为有未保存的更改
    markAsModified();
  }

  function removeAction(id: string) {
    actions = actions.filter((action) => action.id !== id);
    if (actions.length === 0) {
      errorMessage = "";
    }
    markAsModified();
  }

  function moveUpAction(id: string) {
    const index = actions.findIndex((action) => action.id === id);
    if (index > 0) {
      const newActions = [...actions];
      [newActions[index], newActions[index - 1]] = [
        newActions[index - 1],
        newActions[index],
      ];
      actions = newActions;
    }
  }

  function moveDownAction(id: string) {
    const index = actions.findIndex((action) => action.id === id);
    if (index < actions.length - 1) {
      const newActions = [...actions];
      [newActions[index], newActions[index + 1]] = [
        newActions[index + 1],
        newActions[index],
      ];
      actions = newActions;
    }
  }

  function toggleCollapseAction(id: string) {
    actions = actions.map((action) =>
      action.id === id
        ? { ...action, isCollapsed: !action.isCollapsed }
        : action
    );
  }

  function updateActionParams(id: string, params: ActionParams) {
    actions = actions.map((action) =>
      action.id === id ? { ...action, params } : action
    );
    markAsModified();
  }

  function reorderActions(fromIndex: number, toIndex: number) {
    const newActions = [...actions];
    const [movedAction] = newActions.splice(fromIndex, 1);
    newActions.splice(toIndex, 0, movedAction);
    actions = newActions;
    markAsModified();
  }

  function clearActions() {
    actions = [];
    errorMessage = "";
    markAsModified();
  }

  function showError(message: string) {
    errorMessage = message;
  }

  // 动作链管理函数
  function markAsModified() {
    if (actionChainStatus.state === "LOADED") {
      actionChainStatus.state = "MODIFIED";
    } else if (actionChainStatus.state === "EMPTY" && actions.length > 0) {
      actionChainStatus.state = "NEW";
    }
    actionChainStatus.hasUnsavedChanges = true;
  }

  function markAsSaved(name?: string) {
    actionChainStatus.state = "LOADED";
    actionChainStatus.name = name || actionChainStatus.name;
    actionChainStatus.hasUnsavedChanges = false;
  }

  function resetActionChainStatus() {
    actionChainStatus.state = "EMPTY";
    actionChainStatus.name = undefined;
    actionChainStatus.hasUnsavedChanges = false;
  }

  // 新建动作链
  function handleNewActionChain() {
    if (actionChainStatus.hasUnsavedChanges) {
      showConfirm(
        "新建动作链",
        "当前有未保存的更改，确定要新建动作链吗？",
        () => {
          actions = [];
          resetActionChainStatus();
          errorMessage = "";
        }
      );
    } else {
      actions = [];
      resetActionChainStatus();
      errorMessage = "";
    }
  }

  // 清空动作链
  function handleClearActionChain() {
    showConfirm(
      "清空动作",
      "确定要清空所有动作吗？动作链名称将保持不变。",
      () => {
        actions = [];
        // 保持动作链名称，只标记为已修改
        if (actionChainStatus.state === "LOADED") {
          actionChainStatus.state = "MODIFIED";
          actionChainStatus.hasUnsavedChanges = true;
        }
        errorMessage = "";
      }
    );
  }

  // 保存动作链
  function handleSaveActionChain() {
    if (actions.length === 0) {
      alert("没有动作可保存");
      return;
    }

    if (actionChainStatus.state === "NEW" || !actionChainStatus.name) {
      // 新动作链，需要输入名称
      saveDialogMode = "save";
      saveDialogName = "";
      saveDialogDescription = "";
      showSaveDialog = true;
    } else {
      // 已有动作链，直接保存
      performSave(actionChainStatus.name, "");
    }
  }

  // 另存为动作链
  function handleSaveAsActionChain() {
    if (actions.length === 0) {
      alert("没有动作可保存");
      return;
    }

    saveDialogMode = "saveAs";
    saveDialogName = actionChainStatus.name || "";
    saveDialogDescription = "";
    showSaveDialog = true;
  }

  // 执行保存
  async function performSave(name: string, description: string) {
    try {
      const actionData: ActionData[] = actions.map((action, index) => ({
        id: action.id,
        name: action.actionId || action.name, // 优先使用 actionId，回退到 name
        parameters: action.params,
        order: index + 1,
      }));

      await saveActionChain(name, description, actionData);
      markAsSaved(name);
    } catch (error) {
      console.error("保存动作链失败:", error);
      alert(`保存失败: ${error}`);
    }
  }

  // 确认保存对话框
  async function confirmSaveDialog() {
    if (!saveDialogName.trim()) {
      alert("请输入动作链名称");
      return;
    }

    await performSave(saveDialogName.trim(), saveDialogDescription.trim());
    showSaveDialog = false;
  }

  // 取消保存对话框
  function cancelSaveDialog() {
    showSaveDialog = false;
    saveDialogName = "";
    saveDialogDescription = "";
  }

  // 加载动作链
  async function handleLoadActionChain(name: string) {
    if (actionChainStatus.hasUnsavedChanges) {
      showConfirm(
        "加载动作链",
        "当前有未保存的更改，确定要加载新的动作链吗？",
        () => loadActionChainInternal(name)
      );
    } else {
      await loadActionChainInternal(name);
    }
  }

  // 内部加载动作链函数
  async function loadActionChainInternal(name: string) {
    try {
      const chain = await loadActionChain(name);

      // 转换动作数据格式
      actions = chain.actions
        .map((actionData) => {
          // 尝试从注册系统获取动作定义
          const actionDef = registry.getAction(actionData.name);

          if (actionDef) {
            // 使用新的动作系统创建实例
            const actionInstance = registry.createActionInstance(
              actionData.name
            );
            if (actionInstance) {
              // 合并保存的参数和默认参数
              const mergedParams = { ...actionInstance.params };

              // 将保存的参数值覆盖到默认参数上
              if (actionData.parameters) {
                Object.keys(actionData.parameters).forEach((key) => {
                  if (actionData.parameters[key] !== undefined) {
                    mergedParams[key] = actionData.parameters[key];
                  }
                });
              }

              return {
                id: `action-${nextId++}`,
                actionId: actionData.name,
                name: $t(actionDef.nameKey), // 使用翻译后的名称
                description: $t(actionDef.descriptionKey), // 使用翻译后的描述
                params: mergedParams,
                isCollapsed: false,
                inputTypes: actionDef.inputTypes,
                outputTypes: actionDef.outputTypes,
              };
            }
          }

          // 回退处理：如果找不到动作定义，使用原有逻辑
          console.warn(`未找到动作定义: ${actionData.name}`);
          return {
            id: `action-${nextId++}`,
            actionId: actionData.name,
            name: actionData.name,
            description: "",
            params: actionData.parameters || {},
            isCollapsed: false,
            inputTypes: ["video"] as MediaType[],
            outputTypes: ["video"] as MediaType[],
          };
        })
        .filter(Boolean); // 过滤掉可能的 undefined 值

      // 标记为已加载动作链时也需要标记为有修改，因为我们添加了动作
      markAsModified();

      // 更新状态
      markAsSaved(name);

      // 加载时不更新使用统计，只在实际处理时才更新

      // 切换到处理列表Tab
      activeTab = "processing";

      errorMessage = "";
    } catch (error) {
      console.error("加载动作链失败:", error);
      alert(`加载动作链失败: ${error}`);
    }
  }

  // Tab切换处理
  function handleTabChange(tabId: string) {
    activeTab = tabId;
  }

  function closeProgressDialog() {
    showProgressDialog = false;
    currentProcessingAction = "";
    currentActionIndex = 1;
    totalActions = 1;
    isAllCompleted = false;
    isCancelled = false;
    processingErrorMessage = "";
  }

  function handleProcessingError(error: string) {
    processingErrorMessage = error;
    isAllCompleted = false;
    isCancelled = false;
  }

  function cancelProcessing() {
    isCancelled = true;
    console.log("用户取消处理");
    // 调用Rust后端的取消函数
    invoke("cancel_processing").catch((error) => {
      console.warn("调用取消函数失败:", error);
    });
  }

  // 获取输入文件类型
  function getInputFileType(filePath: string): string {
    if (!filePath) return "unknown";

    // 检查是否是文件夹（通过检查是否有文件扩展名）
    const parts = filePath.split(".");
    const hasExtension =
      filePath.includes(".") &&
      parts.length > 1 &&
      parts[parts.length - 1].length > 0;
    if (!hasExtension) {
      // 可能是文件夹
      return "folder";
    }

    const ext = filePath.toLowerCase().split(".").pop();
    if (!ext) return "unknown";

    const videoExts = ["mp4", "avi", "mov", "mkv", "wmv", "flv", "webm", "m4v"];
    const audioExts = ["mp3", "wav", "aac", "flac", "m4a", "ogg", "wma"];
    const imageExts = [
      "jpg",
      "jpeg",
      "png",
      "bmp",
      "gif",
      "webp",
      "tiff",
      "tga",
    ];

    if (videoExts.includes(ext)) return "video";
    if (audioExts.includes(ext)) return "audio";
    if (imageExts.includes(ext)) return "image";

    return "unknown";
  }

  // 验证动作链的输入输出兼容性
  function validateActionChain(inputFileType: string): {
    isValid: boolean;
    errorMessage: string;
  } {
    if (actions.length === 0) {
      return { isValid: true, errorMessage: "" };
    }

    // 检查是否有批量图片转视频动作
    const hasBatchImageAction = actions.some(
      (action) => action.name === "批量图片转视频"
    );
    if (hasBatchImageAction) {
      if (actions.length > 1) {
        return {
          isValid: false,
          errorMessage:
            "批量图片转视频必须作为独立动作使用，不能与其他动作组合",
        };
      }
      // 批量图片转视频需要文件夹输入
      if (inputFileType !== "folder") {
        return {
          isValid: false,
          errorMessage: "批量图片转视频需要选择图片文件夹",
        };
      }
      return { isValid: true, errorMessage: "" };
    }

    let currentInputType = inputFileType;

    for (let i = 0; i < actions.length; i++) {
      const action = actions[i];

      // 检查当前动作是否支持输入类型
      if (!action.inputTypes.includes(currentInputType as MediaType)) {
        return {
          isValid: false,
          errorMessage: `动作"${action.name}"不支持输入类型"${currentInputType}"。支持的输入类型: ${action.inputTypes.join(", ")}`,
        };
      }

      // 更新下一个动作的输入类型为当前动作的输出类型
      if (action.outputTypes.length > 0) {
        // 如果动作支持多种输出类型，输出类型由输入类型决定
        if (action.outputTypes.length > 1) {
          // 对于支持多种输出类型的动作，输出类型与输入类型相同
          currentInputType = currentInputType;
        } else {
          currentInputType = action.outputTypes[0];
        }
      } else {
        return {
          isValid: false,
          errorMessage: `动作"${action.name}"没有定义输出类型`,
        };
      }
    }

    return { isValid: true, errorMessage: "" };
  }

  async function processVideo() {
    // 清除之前的错误信息
    errorMessage = "";

    // 如果当前有加载的动作链，更新使用统计
    if (actionChainStatus.name && actionChainStatus.state !== "NEW") {
      try {
        await updateActionChainUsage(actionChainStatus.name);
      } catch (error) {
        console.error("更新使用统计失败:", error);
        // 不阻止处理继续进行
      }
    }

    // 检查是否有批量图片转视频动作
    const hasBatchImageAction = actions.some(
      (action) => action.name === "批量图片转视频"
    );

    if (actions.length === 0) {
      alert("请先添加处理动作");
      return;
    }

    // 获取输入路径
    const inputPath = videoSelectorComponent.getFilePath();

    // 检查是否选择了文件或文件夹
    if (!inputPath) {
      if (hasBatchImageAction) {
        showError("请先选择图片文件夹");
      } else {
        showError("请先选择一个文件");
      }
      return;
    }

    // 获取输入文件类型并进行链式验证
    const inputFileType = getInputFileType(inputPath);
    const validation = validateActionChain(inputFileType);
    if (!validation.isValid) {
      showError(validation.errorMessage);
      return;
    }

    try {
      console.log("开始处理文件", actions);

      // 重置所有状态
      totalActions = actions.length;
      currentActionIndex = 0; // 先设为0，循环中会正确设置
      currentProcessingAction = "";
      isAllCompleted = false;
      isCancelled = false;
      let currentInputPath = inputPath || "";
      const tempFiles: string[] = [];

      for (let i = 0; i < actions.length; i++) {
        if (isCancelled) {
          console.log("处理已取消");
          break;
        }

        const action = actions[i];
        currentActionIndex = i + 1;
        currentProcessingAction = action.name;

        // 在显示进度对话框之前先进行错误检查
        if (
          action.name === "截取多片段合并" ||
          action.name === "剔除多片段后合并"
        ) {
          // 检查是否有片段
          if (!action.params.segments || action.params.segments.length === 0) {
            throw new Error("请先添加至少一个视频片段");
          }
        }

        showProgressDialog = true;

        let outputPath: string;
        if (i === actions.length - 1) {
          // 最后一个action，根据其输出类型确定最终文件扩展名
          if (action.name === "批量图片转视频") {
            // 批量图片转视频使用文件夹路径生成输出路径
            const folderPath = videoSelectorComponent.getFilePath();
            if (!folderPath) {
              throw new Error("请先选择图片文件夹");
            }
            const folderName = await basename(folderPath);
            const parentDir = await dirname(folderPath);
            outputPath = await join(parentDir, `${folderName}_video.mp4`);
          } else {
            const inputDir = await dirname(inputPath);
            const inputBasename = await basename(inputPath);
            const baseNameWithoutExt = inputBasename.includes(".")
              ? inputBasename.split(".").slice(0, -1).join(".")
              : inputBasename;

            // 根据最后一个action的输出类型确定扩展名
            let finalExt = "mp4"; // 默认扩展名

            // 确定当前action的实际输出类型
            let actualOutputType = action.outputTypes[0];
            if (action.outputTypes.length > 1) {
              // 对于支持多种输出类型的动作，输出类型与输入类型相同
              // 需要根据当前输入类型来确定输出类型
              const currentInputType = getInputFileType(currentInputPath);
              actualOutputType = currentInputType as MediaType;
            }

            if (actualOutputType === "video") {
              finalExt = "mp4";
            } else if (actualOutputType === "audio") {
              // 对于音频输出，检查是否是提取音频action，如果是则使用用户选择的格式
              if (action.name === "提取音频") {
                finalExt = action.params.format || "mp3";
              } else {
                finalExt = "mp3";
              }
            } else if (actualOutputType === "image") {
              finalExt = "png";
            }

            const outBasename = `${baseNameWithoutExt}_output.${finalExt}`;
            outputPath = await join(inputDir, outBasename);
          }
        } else {
          const inputDir = await dirname(inputPath);
          // 对于中间文件，也需要根据当前输入类型确定扩展名
          const currentInputType = getInputFileType(currentInputPath);
          let tempExt = "mp4"; // 默认扩展名

          // 确定当前action的实际输出类型
          let actualOutputType = action.outputTypes[0];
          if (action.outputTypes.length > 1) {
            // 对于支持多种输出类型的动作，输出类型与输入类型相同
            actualOutputType = currentInputType as MediaType;
          }

          if (actualOutputType === "video") {
            tempExt = "mp4";
          } else if (actualOutputType === "audio") {
            tempExt = "mp3";
          } else if (actualOutputType === "image") {
            tempExt = "png";
          }

          const tempBasename = `temp_${i + 1}_${Date.now()}.${tempExt}`;
          outputPath = await join(inputDir, tempBasename);
          tempFiles.push(outputPath);
        }

        // 使用新的动态调用系统
        try {
          // 获取动作定义
          const actionDefinition = registry.getAction(action.actionId);
          if (!actionDefinition) {
            throw new Error(`未找到动作定义: ${action.actionId}`);
          }

          // 验证参数
          if (actionDefinition.validate) {
            const validation = actionDefinition.validate(action.params);
            if (!validation.isValid) {
              // 翻译错误信息
              const translatedErrors = validation.errors.map((errorKey) =>
                $t(errorKey)
              );
              throw new Error(
                `动作"${action.name}"参数验证失败: ${translatedErrors.join(", ")}`
              );
            }
          }

          // 构建执行上下文
          const context: ActionContext = {
            inputPath: currentInputPath,
            outputPath: outputPath,
            isLastAction: i === actions.length - 1,
            actionIndex: i,
            totalActions: actions.length,
            tempFiles: tempFiles,
          };

          // 构建参数
          const invokeParams = actionDefinition.buildParams(
            action.params,
            context
          );

          // 调用后端服务
          await invoke(actionDefinition.invokeCommand, invokeParams);

          if (isCancelled) {
            console.log("处理已取消");
            break;
          }
        } catch (error) {
          console.error(`动作 ${action.name} 执行失败:`, error);
          throw error;
        }

        // 更新当前输入路径（除了某些特殊动作）
        if (
          action.actionId !== "video-to-images" &&
          action.actionId !== "video-to-gif"
        ) {
          currentInputPath = outputPath;
        }
      }

      if (isCancelled) {
        console.log("清理临时文件...");
        for (const tempFile of tempFiles) {
          try {
            await invoke("delete_file", { filePath: tempFile });
          } catch (error) {
            console.warn("清理临时文件失败:", tempFile, error);
          }
        }
        showProgressDialog = false;
        return;
      }

      for (const tempFile of tempFiles) {
        try {
          await invoke("delete_file", { filePath: tempFile });
        } catch (error) {
          console.warn("清理临时文件失败:", tempFile, error);
        }
      }

      console.log("✅ 所有动作处理完成！");

      isAllCompleted = true;
      errorMessage = "";
    } catch (error) {
      console.error("视频处理失败:", error);
      showProgressDialog = false;

      // 重新抛出错误，让ProcessingList处理
      throw error;
    }
  }

  // 使用$effect来在videoSelectorComponent绑定后设置context
  $effect(() => {
    if (videoSelectorComponent) {
      setContext("videoSelectorComponent", videoSelectorComponent);
    }
  });

  onMount(async () => {
    // 初始化动作链管理器
    try {
      await initActionChainManager();
    } catch (error) {
      console.error("初始化动作链管理器失败:", error);
    }

    // 监听批量图片转视频进度
    listen("BATCH_IMAGE_PROGRESS", (event: any) => {
      console.log("批量图片转视频进度:", event.payload.pct);
    });

    // 监听从预览窗口导出的时间段
    listen("segments_exported", (event: any) => {
      console.log("收到导出的时间段:", event.payload);
      handleImportedSegments(event.payload);
    });
  });

  function handleImportedSegments(payload: any) {
    const { segments, actionId } = payload;

    if (segments && segments.length > 0) {
      // 解析时间字符串 (HH:MM:SS)
      const parseTimeString = (timeStr: string) => {
        const parts = timeStr.split(":");
        return {
          hour: parseInt(parts[0]) || 0,
          minute: parseInt(parts[1]) || 0,
          second: parseInt(parts[2]) || 0,
        };
      };

      // 时间字符串转秒数（用于排序）
      const timeStringToSeconds = (timeStr: string): number => {
        const parts = timeStr.split(":");
        const hours = parseInt(parts[0]) || 0;
        const minutes = parseInt(parts[1]) || 0;
        const seconds = parseInt(parts[2]) || 0;
        return hours * 3600 + minutes * 60 + seconds;
      };

      // 查找要更新的action
      let existingActionIndex = -1;
      let targetActionName = "截取多片段合并"; // 默认值

      if (actionId) {
        // 如果有action ID，直接查找对应的action
        existingActionIndex = actions.findIndex(
          (action) => action.id === actionId
        );
        // 如果找到了action，使用其名称
        if (existingActionIndex !== -1) {
          targetActionName = actions[existingActionIndex].name;
        }
      } else {
        // 兼容旧逻辑：查找现有的"截取多片段合并"action
        existingActionIndex = actions.findIndex(
          (action) => action.name === "截取多片段合并"
        );
      }

      if (existingActionIndex !== -1) {
        // 先清空现有片段，再添加新片段
        // 先按开始时间排序
        const sortedSegments = [...segments].sort(
          (a: any, b: any) =>
            timeStringToSeconds(a.startTimeStr) -
            timeStringToSeconds(b.startTimeStr)
        );

        const processedSegments = sortedSegments.map((segment: any) => ({
          startTimeStr: segment.startTimeStr,
          endTimeStr: segment.endTimeStr,
          startTime: parseTimeString(segment.startTimeStr),
          endTime: parseTimeString(segment.endTimeStr),
        }));

        actions[existingActionIndex] = {
          ...actions[existingActionIndex],
          params: {
            ...actions[existingActionIndex].params,
            segments: processedSegments, // 直接替换，不是追加
          },
          description: `从预览导入: ${segments.length}个片段`,
        };

        // 触发响应式更新
        actions = [...actions];
        console.log(
          `成功更新action ${actionId || "(未指定ID)"}，导入了 ${segments.length} 个片段`
        );
        return;
      }

      // 如果没有现有action，创建新的action包含所有片段
      // 先按开始时间排序
      const sortedSegments = [...segments].sort(
        (a: any, b: any) =>
          timeStringToSeconds(a.startTimeStr) -
          timeStringToSeconds(b.startTimeStr)
      );

      const processedSegments = sortedSegments.map((segment: any) => ({
        startTimeStr: segment.startTimeStr,
        endTimeStr: segment.endTimeStr,
        startTime: parseTimeString(segment.startTimeStr),
        endTime: parseTimeString(segment.endTimeStr),
      }));

      const newAction: ActionInstance = {
        id: `trim-${Date.now()}`,
        actionId: targetActionName,
        name: targetActionName,
        description: `从预览导入: ${segments.length}个片段`,
        params: {
          segments: processedSegments,
        },
        isCollapsed: false,
        inputTypes: ["video"] as MediaType[],
        outputTypes: ["video"] as MediaType[],
      };

      actions = [...actions, newAction];
      console.log(`成功创建新action，包含 ${segments.length} 个片段`);
    }
  }

  // 快捷键处理
  function handleKeydown(event: KeyboardEvent) {
    console.log("快捷键按下:", event.key, event.ctrlKey, event.target);

    // 检查是否在输入框中，如果是则不处理全局快捷键
    const target = event.target as HTMLElement;
    const isInInput =
      target.tagName === "INPUT" ||
      target.tagName === "TEXTAREA" ||
      target.contentEditable === "true";

    // F1 - 选择文件
    if (event.key === "F1") {
      console.log("F1按下，videoSelectorComponent:", videoSelectorComponent);
      event.preventDefault();
      if (videoSelectorComponent) {
        videoSelectorComponent.selectFile();
      }
    }
    // F2 - 选择文件夹
    else if (event.key === "F2") {
      console.log("F2按下，videoSelectorComponent:", videoSelectorComponent);
      event.preventDefault();
      if (videoSelectorComponent) {
        videoSelectorComponent.selectFolder();
      }
    }
    // F3 - 定位到搜索栏
    else if (event.key === "F3") {
      console.log("F3按下，actionListComponent:", actionListComponent);
      event.preventDefault();
      if (actionListComponent) {
        actionListComponent.focusSearch();
      }
    }
    // Ctrl+1 - 切换到处理列表Tab
    else if (event.ctrlKey && event.key === "1") {
      event.preventDefault();
      handleTabChange("processing");
    }
    // Ctrl+2 - 切换到动作链列表Tab
    else if (event.ctrlKey && event.key === "2") {
      event.preventDefault();
      handleTabChange("chains");
    }
    // Tab键在搜索框中的特殊处理
    else if (event.key === "Tab" && !isInInput) {
      // 如果不在输入框中，Tab键可以用来在不同区域间导航
      // 这里可以扩展更多的Tab导航逻辑
    }
  }

  onMount(() => {
    // 初始化主题
    theme.init();

    // 添加全局快捷键监听
    document.addEventListener("keydown", handleKeydown);

    return () => {
      document.removeEventListener("keydown", handleKeydown);
    };
  });
</script>

<main class="h-screen bg-background flex flex-col">
  <!-- 顶部标题区域 - 灰色背景 -->
  <div class="flex-none p-3 sm:p-4 bg-muted/30">
    <div class="flex justify-between items-center">
      <div>
        <h1 class="text-xl sm:text-2xl font-bold">视频处理工具</h1>
        <p class="text-muted-foreground text-sm">链式处理视频，支持多种操作</p>
      </div>
      <!-- Tab导航区域 - 移到顶部右侧 -->
      <div class="flex-shrink-0">
        <Tabs {tabs} {activeTab} onTabChange={handleTabChange} />
      </div>
    </div>
  </div>

  <div class="flex-1 flex flex-col lg:flex-row overflow-hidden">
    <!-- 左侧：可用动作列表 - 灰色背景 -->
    <div class="w-full lg:w-80 bg-muted/30 flex-shrink-0 flex flex-col">
      <!-- 动作列表 -->
      <div class="flex-1 flex flex-col min-h-0">
        <ActionList
          bind:this={actionListComponent}
          {actions}
          onAddAction={addAction}
          inputFileType={getInputFileType(selectedFilePath || "")}
        />
      </div>

      <!-- 底部按钮栏 -->
      <div class="flex-none p-3 border-t border-border">
        <div class="flex justify-center gap-2">
          <!-- 主题切换按钮 -->
          <button
            class="p-1.5 text-sm rounded border border-border hover:bg-accent hover:text-accent-foreground transition-colors"
            onclick={() => theme.toggle()}
            title={$theme === "dark" ? "切换到浅色主题" : "切换到深色主题"}
          >
            {$theme === "dark" ? "☀️" : "🌙"}
          </button>

          <!-- 系统设置按钮 -->
          <button
            class="p-1.5 text-sm rounded border border-border hover:bg-accent hover:text-accent-foreground transition-colors"
            onclick={() => {
              // TODO: 打开系统设置页面
              console.log("打开系统设置");
            }}
            title="系统设置"
          >
            ⚙️
          </button>
        </div>
      </div>
    </div>

    <!-- 右侧：Tab内容区域 -->
    <div class="flex-1 flex flex-col overflow-hidden">
      <!-- Tab内容区域 - 白色背景 -->
      <div class="flex-1 overflow-hidden bg-background">
        {#if activeTab === "processing"}
          <!-- 处理列表Tab -->
          <div class="h-full overflow-y-auto">
            <div class="p-3 sm:p-4 space-y-4">
              <!-- 视频选择 -->
              <VideoSelector
                bind:this={videoSelectorComponent}
                externalFilePath={selectedFilePath}
                externalFileInfo={selectedFileInfo}
                onFileChange={handleFileChange}
                on:fileChange={() => {
                  errorMessage = "";
                }}
              />
              {#if errorMessage}
                <div
                  class="mb-4 p-3 bg-red-100 text-red-700 border border-red-300 rounded"
                >
                  <strong>处理错误：</strong>
                  {errorMessage}
                </div>
              {/if}
              <!-- 处理列表 -->
              <ProcessingList
                {actions}
                {actionChainStatus}
                onRemoveAction={removeAction}
                onMoveUpAction={moveUpAction}
                onMoveDownAction={moveDownAction}
                onToggleCollapseAction={toggleCollapseAction}
                onUpdateActionParams={updateActionParams}
                onProcess={processVideo}
                onReorderActions={reorderActions}
                onClearActions={clearActions}
                onShowError={showError}
                onSaveActionChain={handleSaveActionChain}
                onSaveAsActionChain={handleSaveAsActionChain}
                onNewActionChain={handleNewActionChain}
                onClearActionChain={handleClearActionChain}
                inputFileType={videoSelectorComponent
                  ? getInputFileType(videoSelectorComponent.getFilePath() || "")
                  : "video"}
              />
            </div>
          </div>
        {:else if activeTab === "chains"}
          <!-- 动作链列表Tab -->
          <div class="h-full">
            <ActionChainList onLoadActionChain={handleLoadActionChain} />
          </div>
        {/if}
      </div>
    </div>
  </div>

  <!-- 进度对话框 -->
  <ProcessingDialog
    isOpen={showProgressDialog}
    title={`正在处理: ${currentProcessingAction}`}
    {currentActionIndex}
    {totalActions}
    {isAllCompleted}
    {isCancelled}
    errorMessage={processingErrorMessage}
    onClose={closeProgressDialog}
    onCancel={cancelProcessing}
    onError={handleProcessingError}
  />

  <!-- 保存动作链对话框 -->
  {#if showSaveDialog}
    <div
      class="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50"
    >
      <div
        class="bg-background rounded-lg shadow-xl p-6 min-w-[400px] max-w-[90vw]"
      >
        <h3 class="text-lg font-semibold mb-4">
          {saveDialogMode === "save" ? "保存动作链" : "另存为动作链"}
        </h3>

        <div class="space-y-4">
          <div>
            <label class="block text-sm font-medium mb-2">名称</label>
            <Input
              bind:value={saveDialogName}
              placeholder="输入动作链名称"
              class="w-full"
            />
          </div>

          <div>
            <label class="block text-sm font-medium mb-2">描述（可选）</label>
            <Input
              bind:value={saveDialogDescription}
              placeholder="输入动作链描述"
              class="w-full"
            />
          </div>
        </div>

        <div class="flex justify-end gap-2 mt-6">
          <Button variant="outline" onclick={cancelSaveDialog}>取消</Button>
          <Button onclick={confirmSaveDialog}>保存</Button>
        </div>
      </div>
    </div>
  {/if}

  <!-- 确认对话框 -->
  {#if showConfirmDialog}
    <div
      class="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50"
    >
      <div
        class="bg-background rounded-lg shadow-xl p-6 min-w-[400px] max-w-[90vw]"
      >
        <h3 class="text-lg font-semibold mb-4">{confirmDialogTitle}</h3>

        <p class="text-sm text-muted-foreground mb-6">
          {confirmDialogMessage}
        </p>

        <div class="flex justify-end gap-2">
          <Button variant="outline" onclick={cancelConfirm}>取消</Button>
          <Button onclick={confirmAction}>确定</Button>
        </div>
      </div>
    </div>
  {/if}
</main>
